/* You can add global styles to this file, and also import other style files */

/* Global scrolling and layout fixes */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Ensure proper scrolling on all devices */
* {
  box-sizing: border-box;
}

/* Fix for iOS Safari scrolling issues */
body {
  -webkit-overflow-scrolling: touch;
}

/* Ensure form containers can scroll properly */
.form-container {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
