import { Component, OnInit, OnDestroy, AfterViewInit, ElementRef, ViewChild, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { Map, NavigationControl, GeolocateControl, Marker, Popup } from 'maplibre-gl';
import { AuthService } from '../../core/services/auth.service';
import { GeolocationService } from '../../core/services/geolocation.service';
import { PredictionService } from '../../core/services/prediction.service';
import { SupabaseService } from '../../core/services/supabase.service';
import { environment } from '../../../environments/environment';
import { LoadingSpinnerComponent } from '../../shared/components/loading-spinner/loading-spinner.component';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  template: `
    <div class="dashboard-container">
      <!-- Header -->
      <header class="dashboard-header">
        <div class="header-content">
          <div class="header-left">
            <h1>BCTV Management Dashboard</h1>
            <p *ngIf="currentUser">Welcome, {{currentUser.firstName || currentUser.email}}</p>
          </div>
          <div class="header-right">
            <nav class="nav-menu">
              <a routerLink="/data-entry" class="nav-link">📝 Data Entry</a>
              <a routerLink="/predictions" class="nav-link">📊 Predictions</a>
              <button (click)="signOut()" class="nav-link logout-btn">🚪 Sign Out</button>
            </nav>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="dashboard-main">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
          <div class="sidebar-section">
            <h3>Quick Actions</h3>
            <div class="action-buttons">
              <button routerLink="/data-entry/host-plant" class="action-btn">
                🌿 Log Host Plant
              </button>
              <button routerLink="/data-entry/blh" class="action-btn">
                🦗 Log BLH Observation
              </button>
              <button routerLink="/data-entry/bctv" class="action-btn">
                🦠 Log BCTV Symptoms
              </button>
              <button routerLink="/data-entry/eradication" class="action-btn">
                🧹 Log Eradication
              </button>
            </div>
          </div>

          <div class="sidebar-section">
            <h3>Risk Summary</h3>
            <div class="risk-summary" *ngIf="!loadingRisk; else riskLoading">
              <div class="risk-item" *ngFor="let risk of riskSummary">
                <div class="risk-level" [class]="risk.level">{{risk.level}}</div>
                <div class="risk-count">{{risk.count}} areas</div>
              </div>
            </div>
            <ng-template #riskLoading>
              <app-loading-spinner size="small" message="Loading risk data..."></app-loading-spinner>
            </ng-template>
          </div>

          <div class="sidebar-section">
            <h3>Recent Activity</h3>
            <div class="activity-list" *ngIf="!loadingActivity; else activityLoading">
              <div class="activity-item" *ngFor="let activity of recentActivity">
                <div class="activity-icon">{{activity.icon}}</div>
                <div class="activity-content">
                  <div class="activity-title">{{activity.title}}</div>
                  <div class="activity-time">{{activity.time}}</div>
                </div>
              </div>
            </div>
            <ng-template #activityLoading>
              <app-loading-spinner size="small" message="Loading activity..."></app-loading-spinner>
            </ng-template>
          </div>
        </aside>

        <!-- Map Container -->
        <div class="map-container">
          <div class="map-controls">
            <button (click)="centerOnUserLocation()" class="map-control-btn" [disabled]="!userLocation">
              📍 My Location
            </button>
            <button (click)="toggleHeatmap()" class="map-control-btn">
              {{showHeatmap ? '🗺️ Normal View' : '🔥 Heat Map'}}
            </button>
            <button (click)="refreshData()" class="map-control-btn" [disabled]="isRefreshing">
              {{isRefreshing ? '⏳' : '🔄'}} Refresh
            </button>
          </div>

          <div #mapContainer class="map" [style.height.px]="mapHeight"></div>

          <div class="map-legend">
            <h4>Legend</h4>
            <div class="legend-item">
              <div class="legend-marker host-plant"></div>
              <span>Host Plants</span>
            </div>
            <div class="legend-item">
              <div class="legend-marker blh"></div>
              <span>BLH Observations</span>
            </div>
            <div class="legend-item">
              <div class="legend-marker bctv"></div>
              <span>BCTV Symptoms</span>
            </div>
            <div class="legend-item">
              <div class="legend-marker eradication"></div>
              <span>Eradication Efforts</span>
            </div>
          </div>
        </div>
      </main>
    </div>
  `,
  styles: [`
    .dashboard-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: #f5f5f5;
    }

    .dashboard-header {
      background: white;
      border-bottom: 1px solid #ddd;
      padding: 1rem 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0;
      color: #333;
      font-size: 1.5rem;
      font-weight: 700;
    }

    .header-left p {
      margin: 0.25rem 0 0 0;
      color: #666;
      font-size: 0.875rem;
    }

    .nav-menu {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .nav-link {
      padding: 0.5rem 1rem;
      text-decoration: none;
      color: #007bff;
      border-radius: 4px;
      transition: background-color 0.2s;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .nav-link:hover {
      background: #f8f9fa;
    }

    .logout-btn {
      background: none;
      border: none;
      cursor: pointer;
    }

    .dashboard-main {
      flex: 1;
      display: flex;
      overflow: hidden;
    }

    .dashboard-sidebar {
      width: 300px;
      background: white;
      border-right: 1px solid #ddd;
      padding: 1.5rem;
      overflow-y: auto;
    }

    .sidebar-section {
      margin-bottom: 2rem;
    }

    .sidebar-section h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1rem;
      font-weight: 600;
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .action-btn {
      padding: 0.75rem;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      text-align: left;
      font-size: 0.875rem;
      transition: background-color 0.2s;
      text-decoration: none;
      display: block;
    }

    .action-btn:hover {
      background: #0056b3;
    }

    .risk-summary {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .risk-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem;
      border-radius: 4px;
      background: #f8f9fa;
    }

    .risk-level {
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .risk-level.very_high { background: #dc3545; color: white; }
    .risk-level.high { background: #fd7e14; color: white; }
    .risk-level.moderate { background: #ffc107; color: #333; }
    .risk-level.low { background: #28a745; color: white; }
    .risk-level.very_low { background: #6c757d; color: white; }

    .risk-count {
      font-size: 0.875rem;
      color: #666;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .activity-item {
      display: flex;
      gap: 0.75rem;
      align-items: flex-start;
    }

    .activity-icon {
      font-size: 1.25rem;
      width: 2rem;
      text-align: center;
    }

    .activity-content {
      flex: 1;
    }

    .activity-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: #333;
      margin-bottom: 0.25rem;
    }

    .activity-time {
      font-size: 0.75rem;
      color: #666;
    }

    .map-container {
      flex: 1;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .map-controls {
      display: flex;
      gap: 0.5rem;
      padding: 1rem;
      background: white;
      border-bottom: 1px solid #ddd;
    }

    .map-control-btn {
      padding: 0.5rem 1rem;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .map-control-btn:hover:not(:disabled) {
      background: #0056b3;
    }

    .map-control-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .map {
      flex: 1;
      min-height: 400px;
    }

    .map-legend {
      position: absolute;
      bottom: 1rem;
      left: 1rem;
      background: white;
      padding: 1rem;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      min-width: 150px;
    }

    .map-legend h4 {
      margin: 0 0 0.75rem 0;
      font-size: 0.875rem;
      font-weight: 600;
      color: #333;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      font-size: 0.75rem;
      color: #666;
    }

    .legend-marker {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .legend-marker.host-plant { background: #28a745; }
    .legend-marker.blh { background: #ffc107; }
    .legend-marker.bctv { background: #dc3545; }
    .legend-marker.eradication { background: #007bff; }

    @media (max-width: 1024px) {
      .dashboard-sidebar {
        width: 250px;
      }
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }

      .nav-menu {
        justify-content: center;
      }

      .dashboard-main {
        flex-direction: column;
      }

      .dashboard-sidebar {
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
      }

      .map-legend {
        position: relative;
        bottom: auto;
        left: auto;
        margin: 1rem;
      }
    }
  `]
})
export class DashboardComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;

  map!: Map;
  mapHeight = 600;
  currentUser: any = null;
  userLocation: any = null;
  showHeatmap = false;
  isRefreshing = false;
  loadingRisk = true;
  loadingActivity = true;

  riskSummary: any[] = [];
  recentActivity: any[] = [];

  private subscriptions: Subscription[] = [];

  constructor(
    private authService: AuthService,
    private geolocationService: GeolocationService,
    private predictionService: PredictionService,
    private supabaseService: SupabaseService,
    private router: Router
  ) {}

  ngOnInit() {
    this.currentUser = this.authService.currentUser;
    this.calculateMapHeight();
    this.getUserLocation();
    this.loadDashboardData();

    // Listen for navigation events to refresh data when returning to dashboard
    this.subscriptions.push(
      this.router.events.pipe(
        filter(event => event instanceof NavigationEnd)
      ).subscribe((event: NavigationEnd) => {
        if (event.url === '/dashboard') {
          // Refresh data when navigating back to dashboard
          this.refreshData();
        }
      })
    );
  }

  @HostListener('window:focus', ['$event'])
  onWindowFocus(event: any): void {
    // Refresh data when window regains focus (user returns from another tab/app)
    this.refreshData();
  }

  ngAfterViewInit() {
    this.initializeMap();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.map) {
      this.map.remove();
    }
  }

  private calculateMapHeight() {
    // Calculate available height for map
    const headerHeight = 80;
    const controlsHeight = 60;
    const padding = 40;
    this.mapHeight = window.innerHeight - headerHeight - controlsHeight - padding;
  }

  private initializeMap() {
    try {
      this.map = new Map({
        container: this.mapContainer.nativeElement,
        style: environment.maplibre.style,
        center: [-119.4179, 36.7783], // Central California
        zoom: 6,
        attributionControl: false
      });

      // Add navigation controls
      this.map.addControl(new NavigationControl(), 'top-right');

      // Add geolocate control
      this.map.addControl(new GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true
      }), 'top-right');

      this.map.on('load', () => {
        this.loadObservationData();
      });

    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }

  private getUserLocation() {
    this.geolocationService.getCurrentPosition().subscribe({
      next: (location) => {
        this.userLocation = location;
        if (this.map) {
          this.centerOnUserLocation();
        }
      },
      error: (error) => {
        console.warn('Could not get user location:', error);
      }
    });
  }

  private async loadDashboardData() {
    try {
      // Load both risk summary and recent activity data
      await Promise.all([
        this.loadRiskSummary(),
        this.loadRecentActivity()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  private async loadRiskSummary() {
    try {
      this.loadingRisk = true;

      // For now, generate risk summary based on recent observations
      // In a real implementation, this would query a risk_assessments table
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      if (error) throw error;

      // Generate mock risk summary based on observation count
      // This is a simplified approach - real implementation would use prediction service
      const observationCount = observations?.length || 0;

      if (observationCount === 0) {
        this.riskSummary = [
          { level: 'very_high', count: 0 },
          { level: 'high', count: 0 },
          { level: 'moderate', count: 0 },
          { level: 'low', count: 0 },
          { level: 'very_low', count: 0 }
        ];
      } else {
        // Generate risk distribution based on observation density
        this.riskSummary = [
          { level: 'very_high', count: Math.floor(observationCount * 0.1) },
          { level: 'high', count: Math.floor(observationCount * 0.2) },
          { level: 'moderate', count: Math.floor(observationCount * 0.3) },
          { level: 'low', count: Math.floor(observationCount * 0.25) },
          { level: 'very_low', count: Math.floor(observationCount * 0.15) }
        ];
      }

    } catch (error) {
      console.error('Error loading risk summary:', error);
      this.riskSummary = [];
    } finally {
      this.loadingRisk = false;
    }
  }

  private async loadRecentActivity() {
    try {
      this.loadingActivity = true;

      // Load recent observations for activity feed
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('type, created_at')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      // Convert observations to activity items
      this.recentActivity = observations?.map(obs => ({
        icon: this.getActivityIcon(obs.type),
        title: this.getActivityTitle(obs.type),
        time: this.getRelativeTime(obs.created_at)
      })) || [];

      // If no observations, show empty state
      if (this.recentActivity.length === 0) {
        this.recentActivity = [{
          icon: '📝',
          title: 'No recent activity',
          time: 'Start by adding your first observation'
        }];
      }

    } catch (error) {
      console.error('Error loading recent activity:', error);
      this.recentActivity = [{
        icon: '⚠️',
        title: 'Error loading activity',
        time: 'Please refresh the page'
      }];
    } finally {
      this.loadingActivity = false;
    }
  }

  private async loadObservationData() {
    try {
      // Load recent observations from Supabase
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Clear existing markers first
      // Note: In a real implementation, you'd want to track markers to remove them

      // Add markers for each observation
      observations?.forEach(obs => {
        this.addObservationMarker(obs);
      });

    } catch (error) {
      console.error('Error loading observation data:', error);
    }
  }

  private addObservationMarker(observation: any) {
    const color = this.getMarkerColor(observation.type);
    const popup = new Popup({ offset: 25 }).setHTML(`
      <div>
        <h4>${this.getObservationTitle(observation.type)}</h4>
        <p><strong>Date:</strong> ${new Date(observation.created_at).toLocaleDateString()}</p>
        <p><strong>Location:</strong> ${observation.latitude.toFixed(4)}, ${observation.longitude.toFixed(4)}</p>
        ${observation.notes ? `<p><strong>Notes:</strong> ${observation.notes}</p>` : ''}
      </div>
    `);

    new Marker({ color })
      .setLngLat([observation.longitude, observation.latitude])
      .setPopup(popup)
      .addTo(this.map);
  }

  private getMarkerColor(type: string): string {
    switch (type) {
      case 'host_plant': return '#28a745';
      case 'blh_observation': return '#ffc107';
      case 'bctv_symptoms': return '#dc3545';
      case 'eradication_effort': return '#007bff';
      default: return '#6c757d';
    }
  }

  private getObservationTitle(type: string): string {
    switch (type) {
      case 'host_plant': return 'Host Plant Observation';
      case 'blh_observation': return 'BLH Observation';
      case 'bctv_symptoms': return 'BCTV Symptoms';
      case 'eradication_effort': return 'Eradication Effort';
      default: return 'Observation';
    }
  }

  private getActivityIcon(type: string): string {
    switch (type) {
      case 'host_plant': return '🌿';
      case 'blh_observation': return '🦗';
      case 'bctv_symptoms': return '🦠';
      case 'eradication_effort': return '🧹';
      default: return '📝';
    }
  }

  private getActivityTitle(type: string): string {
    switch (type) {
      case 'host_plant': return 'Host plant logged';
      case 'blh_observation': return 'BLH observation recorded';
      case 'bctv_symptoms': return 'BCTV symptoms detected';
      case 'eradication_effort': return 'Eradication completed';
      default: return 'Observation recorded';
    }
  }

  private getRelativeTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  centerOnUserLocation() {
    if (this.userLocation && this.map) {
      this.map.flyTo({
        center: [this.userLocation.longitude, this.userLocation.latitude],
        zoom: 12,
        duration: 1000
      });
    }
  }

  toggleHeatmap() {
    this.showHeatmap = !this.showHeatmap;
    // In a real implementation, this would toggle heatmap layers
    console.log('Heatmap toggled:', this.showHeatmap);
  }

  refreshData() {
    this.isRefreshing = true;

    // Refresh all dashboard data
    Promise.all([
      this.loadDashboardData(),
      this.loadObservationData()
    ]).finally(() => {
      this.isRefreshing = false;
    });
  }

  signOut() {
    this.authService.signOut().subscribe();
  }
}
